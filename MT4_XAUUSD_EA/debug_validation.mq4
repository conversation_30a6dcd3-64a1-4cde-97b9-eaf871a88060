//+------------------------------------------------------------------+
//|                                           debug_validation.mq4   |
//|                                    调试安全验证失败问题           |
//+------------------------------------------------------------------+
#property copyright "Debug Script"
#property version   "1.00"
#property strict

// 模拟EA的全局变量
double MaxLossUSD = 100.0;
double RiskPercentage = 2.0;
bool CurrentUsePercentageRisk = false;
bool IsLongMode = true;

//+------------------------------------------------------------------+
//| 脚本主函数                                                        |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== 调试安全验证失败问题 ===");
    
    // 获取当前市场信息
    double currentPrice = (Bid + Ask) / 2.0;
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    double tickSize = MarketInfo(Symbol(), MODE_TICKSIZE);
    double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
    
    Print("\n=== 市场信息 ===");
    Print("当前价格: ", currentPrice);
    Print("最小手数: ", minLot);
    Print("最大手数: ", maxLot);
    Print("点值: ", tickValue);
    Print("最小变动: ", tickSize);
    Print("最小止损距离: ", minStopLevel);
    Print("账户余额: $", AccountBalance());
    
    // 模拟EA的参数设置
    double testLots = 0.1;
    double testSL = currentPrice - 50 * Point; // 50点止损
    double testTP = currentPrice + 100 * Point; // 100点止盈
    
    Print("\n=== 测试参数 ===");
    Print("测试手数: ", testLots);
    Print("测试止损: ", testSL, " (距离: ", MathAbs(currentPrice - testSL), ")");
    Print("测试止盈: ", testTP, " (距离: ", MathAbs(currentPrice - testTP), ")");
    
    // 测试基本参数验证
    Print("\n=== 基本参数验证测试 ===");
    bool basicValid = TestValidateTradeParameters(testLots, currentPrice, testSL, testTP);
    Print("基本验证结果: ", basicValid ? "通过" : "失败");
    
    // 测试风险控制验证
    Print("\n=== 风险控制验证测试 ===");
    bool riskValid = TestRiskControlValidation(testLots, currentPrice, testSL, testTP);
    Print("风险验证结果: ", riskValid ? "通过" : "失败");
    
    // 测试边界条件
    Print("\n=== 边界条件测试 ===");
    
    // 测试1：手数为0
    Print("\n测试1：手数为0");
    TestValidateTradeParameters(0.0, currentPrice, testSL, testTP);
    
    // 测试2：止损距离过小
    Print("\n测试2：止损距离过小");
    double tooCloseSL = currentPrice - (minStopLevel / 2);
    TestValidateTradeParameters(testLots, currentPrice, tooCloseSL, testTP);
    
    // 测试3：盈亏比过低
    Print("\n测试3：盈亏比过低");
    double lowRRTP = currentPrice + 10 * Point; // 很小的止盈
    TestRiskControlValidation(testLots, currentPrice, testSL, lowRRTP);
    
    Print("\n=== 调试完成 ===");
}

//+------------------------------------------------------------------+
//| 测试基本参数验证                                                  |
//+------------------------------------------------------------------+
bool TestValidateTradeParameters(double lots, double entryPrice, double stopLoss, double takeProfit)
{
    Print("--- 基本参数验证 ---");
    
    // 参数合理性检查：手数
    if(lots <= 0)
    {
        Print("错误：手数无效 (", lots, ")");
        return false;
    }
    
    // 参数合理性检查：手数范围
    double minLot = MarketInfo(Symbol(), MODE_MINLOT);
    double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
    Print("手数检查: ", lots, " (范围: ", minLot, " - ", maxLot, ")");
    
    if(lots < minLot || lots > maxLot)
    {
        Print("错误：手数超出范围，当前:", lots, " 最小:", minLot, " 最大:", maxLot);
        return false;
    }

    // 参数合理性检查：价格有效性
    Print("价格检查: 入场=", entryPrice, " 止损=", stopLoss, " 止盈=", takeProfit);
    
    if(entryPrice <= 0 || stopLoss <= 0 || takeProfit <= 0)
    {
        Print("错误：价格参数无效");
        return false;
    }

    // 参数合理性检查：止损止盈距离
    double minStopLevel = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
    double slDistance = MathAbs(entryPrice - stopLoss);
    double tpDistance = MathAbs(entryPrice - takeProfit);
    
    Print("距离检查: 止损距离=", slDistance, " 止盈距离=", tpDistance, " 最小要求=", minStopLevel);
    
    if(slDistance < minStopLevel)
    {
        Print("错误：止损距离过小，当前:", slDistance, " 最小距离:", minStopLevel);
        return false;
    }

    if(tpDistance < minStopLevel)
    {
        Print("错误：止盈距离过小，当前:", tpDistance, " 最小距离:", minStopLevel);
        return false;
    }

    Print("✓ 所有基本参数验证通过");
    return true;
}

//+------------------------------------------------------------------+
//| 测试风险控制验证                                                  |
//+------------------------------------------------------------------+
bool TestRiskControlValidation(double lots, double entryPrice, double stopLoss, double takeProfit)
{
    Print("=== 开始风险控制验证 ===");
    Print("手数: ", lots, " 入场价: ", entryPrice, " 止损: ", stopLoss, " 止盈: ", takeProfit);
    Print("交易方向: ", (IsLongMode ? "做多" : "做空"));
    
    // 基本参数验证
    if(!TestValidateTradeParameters(lots, entryPrice, stopLoss, takeProfit))
    {
        Print("基本参数验证失败");
        return false;
    }
    Print("✓ 基本参数验证通过");

    // 风险控制：最大风险限制检查
    double riskDistance = MathAbs(entryPrice - stopLoss);
    double tickValue = MarketInfo(Symbol(), MODE_TICKVALUE);
    double tickSize = MarketInfo(Symbol(), MODE_TICKSIZE);
    
    Print("风险距离: ", riskDistance, " 点值: ", tickValue, " 最小变动: ", tickSize);
    
    if(tickValue > 0 && tickSize > 0)
    {
        double actualRiskUSD = (riskDistance / tickSize) * tickValue * lots;
        double maxAllowedRisk = CurrentUsePercentageRisk ? 
            (AccountBalance() * RiskPercentage / 100.0) : MaxLossUSD;
            
        Print("实际风险: $", DoubleToString(actualRiskUSD, 2), " 最大允许: $", DoubleToString(maxAllowedRisk, 2));
            
        if(actualRiskUSD > maxAllowedRisk)
        {
            Print("错误：风险超出限制，实际风险: $", DoubleToString(actualRiskUSD, 2), 
                  " 最大允许: $", DoubleToString(maxAllowedRisk, 2));
            return false;
        }
        Print("✓ 风险限制检查通过");
    }
    else
    {
        Print("警告：无法获取有效的点值或最小变动单位");
    }

    // 风险控制：盈亏比检查（考虑交易方向）
    double profitDistance;
    if(IsLongMode)
    {
        // 做多：止盈应该高于入场价
        profitDistance = takeProfit - entryPrice;
    }
    else
    {
        // 做空：止盈应该低于入场价
        profitDistance = entryPrice - takeProfit;
    }
    
    double currentRR = (riskDistance > 0) ? profitDistance / riskDistance : 0.0;
    
    Print("盈利距离: ", profitDistance, " 盈亏比: ", DoubleToString(currentRR, 2));
    
    if(currentRR < 0.5) // 最低盈亏比要求
    {
        Print("错误：盈亏比过低 (", DoubleToString(currentRR, 2), ")，最低要求 0.5");
        return false;
    }
    Print("✓ 盈亏比检查通过");

    Print("=== 风险控制验证完成 ===");
    return true;
}
